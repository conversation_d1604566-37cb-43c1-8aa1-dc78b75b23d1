import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { getAuth, getIdTokenResult, reload } from 'firebase/auth';
import React, { useContext, useState } from "react";
import { Linking, Platform, RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { ActivityIndicator, Button, Divider, List, Text } from "react-native-paper";
import DeleteAccountDialog from '../../../components/DeleteAccountDialog';
import DeleteAccountSignInAgainDialog from '../../../components/DeleteAccountSignInAgainDialog';
import UserSubscriptionHeader from '../../../components/profile/UserSubscriptionHeader';
import SignOutConfirmationDialog from '../../../components/SignOutConfirmationDialog';
import SignUpPromptDialog from '../../../components/SignUpPromptDialog';
import { ApiService } from "../../../services/api/apiService";
import { ApiError } from '../../../services/api/models/apiError';
import { UserDetails } from '../../../services/api/models/userDetails';
import { authSelectors, signOutUser } from '../../../services/authService';
import useGlobalStore from '../../../services/globalState';
import { apiServiceContext, translationServiceContext, whatsappServiceContext } from "../../../services/provider";
import { TranslationService } from '../../../services/translationService';
import { WhatsappService } from '../../../services/whatsappService';
import { WHATSAPP_HELP_MSG, WHATSAPP_SUBSCRIBE_MSG } from '../../../utils/constants';
import { usePurchaseFlow } from '../../../utils/hooks/usePurchaseFlow';

type AccountScreenProps = CompositeScreenProps<
    BottomTabScreenProps<AccountStackParamList, 'Account'>,
    NativeStackScreenProps<RootStackParamList>
>;

interface GuestModeAccountViewProps {
    onCreateAccount: () => void;
    translationService: TranslationService;
}

const GuestModeAccountView: React.FC<GuestModeAccountViewProps> = ({ onCreateAccount, translationService }) => {
    return (
        <View style={styles.guestModeContainer}>
            <View style={styles.guestModeContent}>
                <List.Icon icon="account-star" size={80} />
                <Text variant="headlineSmall" style={styles.guestModeTitle}>
                    Mode invité
                </Text>
                <Text variant="bodyLarge" style={styles.guestModeDescription}>
                    Créez un compte gratuit pour accéder à toutes les fonctionnalités de PharmaChainage
                </Text>
                <View style={styles.guestModeBenefits}>
                    <List.Item
                        title="7 jours d'essai complet"
                        left={() => <List.Icon icon="calendar-check" />}
                        titleStyle={styles.benefitText}
                        style={styles.benefitItem}
                    />
                    <List.Item
                        title="Jusqu'à 150 recherches/jour"
                        left={() => <List.Icon icon="magnify-plus" />}
                        titleStyle={styles.benefitText}
                        style={styles.benefitItem}
                    />
                    <List.Item
                        title="Sauvegarder vos chaînages"
                        left={() => <List.Icon icon="content-save" />}
                        titleStyle={styles.benefitText}
                        style={styles.benefitItem}
                    />
                </View>
                <Button
                    mode="contained"
                    onPress={onCreateAccount}
                    icon="account-plus"
                    style={styles.createAccountButton}
                >
                    Créer un compte gratuit
                </Button>
            </View>
        </View>
    );
};

const AccountScreen: React.FC<AccountScreenProps> = ({ navigation }) => {

    const translationService: TranslationService = useContext(translationServiceContext);
    const whatsappService: WhatsappService = useContext(whatsappServiceContext);
    const apiService: ApiService = useContext(apiServiceContext);

    const { userUid, isUserAnonymous } = useGlobalStore(authSelectors.authenticatedUser);

    const { startPurchaseFlow } = usePurchaseFlow();

    const {
        user: userDetails,
        showSnackbar,
        update,
        completeOperation
    } = useGlobalStore();

    const [refreshing, setRefreshing] = useState<boolean>(false);
    const [deleteAccountDialogVisible, setDeleteAccountDialogVisible] = useState<boolean>(false);
    const [deleteAccountSignInAgainDialogVisible, setDeleteAccountSignInAgainDialogVisible] = useState<boolean>(false);

    // todo create a hook and use it
    const refreshUserData = async (): Promise<void> => {
        setRefreshing(true);
        try {
            await getAuth().currentUser?.reload(); // Ensure user info is refreshed
            const response = await apiService.getUserDetails(userUid);
            if (response.ok) {
                const userDetails: UserDetails = await response.json();
                update(userDetails);
            } else {
                throw new Error("Failed to fetch user details");
            }
        } catch (error) {
            console.error("Failed to refresh user data:", error);
            showSnackbar(translationService.translate("FAILED_TO_REFRESH_USER_DATA"), "error");
            throw error; // Propagate the error to the caller
        } finally {
            setRefreshing(false);
        }
    };

    const [isSigningOut, setIsSigningOut] = useState<boolean>(false);
    const [signOutDialogVisible, setSignOutDialogVisible] = useState<boolean>(false);
    const [signUpPromptVisible, setSignUpPromptVisible] = useState<boolean>(false);

    const performSignOut = async () => {
        try {
            setIsSigningOut(true);
            showSnackbar(translationService.translate("SIGN_OUT_IN_PROGRESS"), "info");

            await signOutUser();

            // Show success message briefly before navigation completes
            showSnackbar(translationService.translate("SIGN_OUT_SUCCESS"), "success");
        } catch (error) {
            console.error("Sign out failed:", error);
            showSnackbar(translationService.translate("SIGN_OUT_FAILED"), "error");
        } finally {
            setIsSigningOut(false);
            setSignOutDialogVisible(false);
        }
    };

    const onSignOut = () => {
        setSignOutDialogVisible(true);
    };

    const deleteUser = async () => {
        const response = await apiService.deleteUser(userUid);
        if (400 === response.status) {
            return response.json()
                .then((apiError: ApiError) => {
                    if ("CREDENTIAL_TOO_OLD_LOGIN_AGAIN" === apiError.code) {
                        setDeleteAccountDialogVisible(false);
                        setDeleteAccountSignInAgainDialogVisible(true);
                    }
                });
        } else if (response.ok) {
            performSignOut();
            completeOperation(`DELETE_USER_${userUid}`, translationService.translate("DELETE_USER_SUCCESS"), 'success');
        } else {
            console.error("Something went wrong, cannot delete user.");
            completeOperation(`DELETE_USER_${userUid}`, translationService.translate("DELETE_USER_ERROR"), 'error');
        }
    };

    return (
        <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={refreshUserData} />}>
            <SignOutConfirmationDialog
                visible={signOutDialogVisible}
                onConfirm={performSignOut}
                onDismiss={() => setSignOutDialogVisible(false)}
                isLoading={isSigningOut}
                translationService={translationService}
            />
            <DeleteAccountSignInAgainDialog
                visible={deleteAccountSignInAgainDialogVisible}
                onSignInAgain={performSignOut}
                onDismiss={() => setDeleteAccountSignInAgainDialogVisible(false)}
            />
            <DeleteAccountDialog
                visible={deleteAccountDialogVisible}
                onDelete={deleteUser}
                onDismiss={() => setDeleteAccountDialogVisible(false)}
            />
            <SignUpPromptDialog
                visible={signUpPromptVisible}
                onDismiss={() => setSignUpPromptVisible(false)}
            />
            {isUserAnonymous ? (
                // Guest mode view - show account creation prompt
                <GuestModeAccountView
                    onCreateAccount={() => setSignUpPromptVisible(true)}
                    translationService={translationService}
                />
            ) : (
                // Authenticated user view - show normal account menu
                <>
                    {userDetails && (
                        <View style={styles.itemPadding}>
                            <View style={styles.profileHeaderContainer}>
                                <UserSubscriptionHeader
                                    user={userDetails}
                                    subscribeButtonIcon={Platform.OS === 'ios' ? 'whatsapp' : 'credit-card-outline'}
                                    onSubscribe={() => {
                                        if (Platform.OS === 'ios') {
                                            Linking.openURL(whatsappService.generateMsgLink(WHATSAPP_SUBSCRIBE_MSG))
                                        } else {
                                            startPurchaseFlow(userUid);
                                        }
                                    }}
                                />
                            </View>
                            <Divider />
                        </View>
                    )}
                    <View style={styles.itemPadding}>
                        <List.Item
                            title={translationService.translate("PERSONAL_INFO")}
                            titleStyle={styles.underline}
                            onPress={() => {
                                navigation.navigate({
                                    name: "UserPersonalInfo",
                                    params: {}
                                });
                            }}
                            left={() => <List.Icon icon="account" />}
                            style={styles.paddingRightLeft}
                        />
                    </View>
                    <Divider />
                    <View style={styles.itemPadding}>
                        <List.Item
                            title={"Gérer mon abonnement"}
                            titleStyle={styles.underline}
                            onPress={() => navigation.navigate("Subscription")}
                            left={() => <List.Icon icon="star" />}
                            style={styles.paddingRightLeft}
                        />
                    </View>
                    <Divider />
                    <View style={styles.itemPadding}>
                        <List.Item
                            title={translationService.translate("NEED_HELP")}
                            titleStyle={styles.underline}
                            description={translationService.translate("NEED_HELP_DESCRIPTION")}
                            onPress={() => Linking.openURL(whatsappService.generateMsgLink(WHATSAPP_HELP_MSG))}
                            left={() => <List.Icon icon="face-agent" />}
                            style={styles.paddingRightLeft}
                        />
                    </View>
                    <Divider />
                    <View style={styles.itemPadding}>
                        <List.Item
                            title={translationService.translate("DELETE_YOUR_ACCOUNT")}
                            titleStyle={styles.underline}
                            onPress={() => {
                                setDeleteAccountDialogVisible(true);
                            }}
                            left={() => <List.Icon icon="delete" />}
                            style={styles.paddingRightLeft}
                        />
                    </View>
                    <Divider />
                    <View style={styles.itemPadding}>
                        <List.Item
                            testID="profileSignOutButton"
                            title={translationService.translate("SIGN_OUT")}
                            onPress={onSignOut}
                            disabled={isSigningOut}
                            titleStyle={[styles.bold, styles.underline, styles.signOutTextColor]}
                            left={() => <List.Icon icon={isSigningOut ? "loading" : "logout"} />}
                            right={() => isSigningOut && <ActivityIndicator animating size="small" />}
                            style={styles.paddingRightLeft}
                        />
                    </View>
                    <Divider />
                </>
            )}
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    underline: {
        textDecorationLine: 'underline'
    },
    bold: {
        fontWeight: 'bold'
    },
    profileHeaderContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 24,
        gap: 4,
    },
    profileSubHeaderContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    paddingRightLeft: {
        paddingHorizontal: 8,
    },
    descriptionView: {
        flex: 1,
        gap: 8,
    },
    placeType: {
        fontWeight: 'bold',
    },
    itemPadding: {
        paddingHorizontal: 8
    },
    signOutTextColor: {
        color: '#D32F2F'
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 8,
    },
    badge: {
        position: 'absolute',
        bottom: -5,
        right: -5,
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 12,
        borderWidth: 2,
        zIndex: 1,
    },
    badgeText: {
        fontWeight: 'bold',
        fontSize: 12,
    },
    loadingDialogContent: {
        alignItems: 'center',
        padding: 16,
    },
    loadingText: {
        marginTop: 16,
        textAlign: 'center',
    },
});

export default AccountScreen;
